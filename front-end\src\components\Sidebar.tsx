import React from 'react';
import {
  BookOpen,
  FileText,
  Video,
  MessageSquare,
  Link2,
  Image,
  ShoppingCart,
  Users,
  Package,
  DollarSign,
  MapPin,
  Calculator,
  Clock,
  User,
  Sparkles,
  LogOut,
  CreditCard,
  Home,
  Lock,
  CheckCircle
} from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';

interface SidebarProps {
  activeItem: string;
  onItemClick: (item: string) => void;
  onLockedItemClick?: (item: string) => void;
}

const Sidebar: React.FC<SidebarProps> = ({ activeItem, onItemClick, onLockedItemClick }) => {
  const { user, logout } = useAuth();

  const handleLogout = async () => {
    await logout();
  };

  // Check if user has active subscription
  const hasActiveSubscription = user?.has_subscription &&
    user?.subscription_status === 'active' &&
    user?.subscription_end &&
    new Date(user.subscription_end) > new Date();

  // Check if item is locked for non-subscribed users
  const isItemLocked = (itemId: string) => {
    if (user?.role === 'admin') return false; // Admin never locked
    if (itemId === 'dashboard') return false; // Dashboard always accessible

    // Lock all tools and business management features for non-subscribers
    const lockedItems = [
      'wa-testimonial-generator',
      'ads-image-generator',
      'marketing-content-generator',
      'peta-cuan',
      'kalkulator-hpp'
    ];

    return lockedItems.includes(itemId) && !hasActiveSubscription;
  };
  const menuSections = [
    // Dashboard untuk semua user
    {
      title: "MAIN",
      items: [
        { id: 'dashboard', label: 'Dashboard', icon: Home },
        ...(user?.role !== 'admin' ? [
          { id: 'invoices', label: 'Riwayat Tagihan', icon: FileText },
        ] : []),
      ]
    },
    // Show Tools & Manajemen Bisnis hanya untuk non-admin
    ...(user?.role !== 'admin' ? [
      {
        title: "TOOLS GENERATOR",
        items: [
          { id: 'wa-testimonial-generator', label: 'WA Testimonial Generator', icon: MessageSquare },
          { id: 'ads-image-generator', label: 'Ads Image Generator', icon: Image },
          { id: 'marketing-content-generator', label: 'Marketing Content Generator', icon: Sparkles },
        ]
      },
      {
        title: "MANAJEMEN BISNIS",
        items: [
          { id: 'peta-cuan', label: 'Peta Cuan Lokasi', icon: MapPin },
          { id: 'kalkulator-hpp', label: 'Kalkulator HPP Otomatis', icon: Calculator },
        ]
      }
    ] : []),
    ...(user?.role === 'admin' ? [{
      title: "ADMIN PANEL",
      items: [
        { id: 'user-management', label: 'User Management', icon: Users },
        { id: 'payment-methods', label: 'Payment Methods', icon: CreditCard },
        { id: 'subscription-packages', label: 'Subscription Packages', icon: Package },
        { id: 'payment-verification', label: 'Verifikasi Pembayaran', icon: CheckCircle },
      ]
    }] : [])
  ];

  return (
    <div className="w-64 bg-white border-r border-gray-200 h-screen flex flex-col">
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center gap-2">
          <div className="w-8 h-8 rounded-full overflow-hidden flex items-center justify-center">
            <img
              src="/logo.png"
              alt="Santuy Grow Logo"
              className="w-full h-full object-contain"
            />
          </div>
          <span className="font-semibold text-gray-800">Santuy Grow</span>
        </div>
      </div>

      <div className="p-4 flex-1 overflow-y-auto">
        {menuSections.map((section) => (
          <div key={section.title} className="mb-6">
            <h3 className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-3">
              {section.title}
            </h3>
            <div className="space-y-1">
              {section.items.map((item) => {
                const Icon = item.icon;
                const isActive = activeItem === item.id;
                const isLocked = isItemLocked(item.id);

                return (
                  <button
                    key={item.id}
                    onClick={() => {
                      if (isLocked && onLockedItemClick) {
                        onLockedItemClick(item.id);
                      } else {
                        onItemClick(item.id);
                      }
                    }}
                    className={`w-full flex items-center justify-between px-3 py-2 text-sm rounded-lg transition-colors text-left ${isActive
                      ? 'bg-green-50 text-green-700 border-l-2 border-green-500'
                      : item.highlight
                        ? 'bg-orange-50 text-orange-700'
                        : isLocked
                          ? 'text-gray-400 hover:bg-gray-50 cursor-pointer'
                          : 'text-gray-600 hover:bg-gray-50'
                      }`}
                  >
                    <div className="flex items-center gap-3">
                      <Icon className="w-4 h-4" />
                      <span>{item.label}</span>
                    </div>
                    {isLocked && (
                      <Lock className="w-4 h-4 text-gray-400" />
                    )}
                  </button>
                );
              })}
            </div>
          </div>
        ))}
      </div>

      <div className="p-4 border-t border-gray-200">
        <div className="flex items-center gap-3 px-3 py-2">
          <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full flex items-center justify-center">
            <span className="text-white font-medium text-sm">
              {user?.name?.charAt(0).toUpperCase() || 'U'}
            </span>
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-800 truncate">{user?.name || 'User'}</p>
            <p className="text-xs text-gray-500 truncate">{user?.email || '<EMAIL>'}</p>
          </div>
          <button
            onClick={handleLogout}
            className="p-1 text-gray-400 hover:text-red-500 hover:bg-gray-50 rounded transition-colors"
            title="Logout"
          >
            <LogOut className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;